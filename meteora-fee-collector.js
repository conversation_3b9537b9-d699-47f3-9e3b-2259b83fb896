/**
 * 🎯 METEORA FEE COLLECTOR - ОТДЕЛЬНЫЙ СБОР КОМИССИЙ
 * 
 * Собирает комиссии только с низкокомиссионного пула (0.04%)
 * Запускается отдельно от основной арбитражной транзакции
 */

const { Connection, Keypair, PublicKey, TransactionMessage, VersionedTransaction } = require('@solana/web3.js');
const { getAssociatedTokenAddress } = require('@solana/spl-token');
const DLMM = require('@meteora-ag/dlmm').default;
const fs = require('fs');
const path = require('path');

// Импорты наших модулей
const { globalRPCManager } = require('./centralized-rpc-manager.js');
const MeteoraBinCacheManager = require('./meteora-bin-cache-manager-clean');

class MeteoraFeeCollector {
    constructor() {
        console.log('🎯 METEORA FEE COLLECTOR ИНИЦИАЛИЗАЦИЯ...');
        
        // 🔑 ЗАГРУЖАЕМ WALLET
        this.loadWallet();
        
        // 🌐 RPC CONNECTION
        this.connection = globalRPCManager.getConnection();
        
        // 🏊 НИЗКОКОМИССИОННЫЙ ПУЛ (0.04%)
        this.LOW_FEE_POOL = {
            address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
            name: 'Pool 1 (Low Fee 0.04%)',
            fee: 0.0004
        };
        
        // 🎯 ПОЗИЦИИ ДЛЯ СБОРА КОМИССИЙ
        this.POSITION_KEYPAIRS = {
            POOL_1: null // Будет загружен из файла
        };
        
        // 📊 КЭШ МЕНЕДЖЕР
        this.binCacheManager = new MeteoraBinCacheManager(this.connection);
        
        console.log(`✅ Fee Collector инициализирован для пула: ${this.LOW_FEE_POOL.name}`);
        console.log(`📍 Адрес пула: ${this.LOW_FEE_POOL.address}`);
    }
    
    /**
     * 🔑 ЗАГРУЗКА WALLET
     */
    loadWallet() {
        try {
            const walletPath = path.join(__dirname, 'wallet.json');
            if (!fs.existsSync(walletPath)) {
                throw new Error('Файл wallet.json не найден');
            }
            
            const walletData = JSON.parse(fs.readFileSync(walletPath, 'utf8'));
            this.wallet = Keypair.fromSecretKey(new Uint8Array(walletData));
            
            console.log(`🔑 Wallet загружен: ${this.wallet.publicKey.toString().slice(0,8)}...`);
        } catch (error) {
            console.error('❌ Ошибка загрузки wallet:', error.message);
            throw error;
        }
    }
    
    /**
     * 🎯 ЗАГРУЗКА POSITION KEYPAIRS
     */
    loadPositionKeypairs() {
        try {
            const positionsPath = path.join(__dirname, 'meteora-positions.json');
            if (!fs.existsSync(positionsPath)) {
                console.log('⚠️ Файл meteora-positions.json не найден - создаем новые позиции');
                return;
            }
            
            const positionsData = JSON.parse(fs.readFileSync(positionsPath, 'utf8'));
            
            if (positionsData.POOL_1) {
                this.POSITION_KEYPAIRS.POOL_1 = Keypair.fromSecretKey(
                    new Uint8Array(positionsData.POOL_1)
                );
                console.log(`🎯 Pool 1 позиция: ${this.POSITION_KEYPAIRS.POOL_1.publicKey.toString().slice(0,8)}...`);
            }
            
        } catch (error) {
            console.error('❌ Ошибка загрузки позиций:', error.message);
        }
    }
    
    /**
     * 🔍 ПРОВЕРКА НАКОПЛЕННЫХ КОМИССИЙ
     */
    async checkAccumulatedFees() {
        try {
            console.log('🔍 ПРОВЕРКА НАКОПЛЕННЫХ КОМИССИЙ...');
            
            // Загружаем позиции
            this.loadPositionKeypairs();
            
            if (!this.POSITION_KEYPAIRS.POOL_1) {
                console.log('⚠️ Нет позиций для сбора комиссий');
                return { hasFees: false, amount: 0 };
            }
            
            // Создаем DLMM instance
            const dlmmPool = await DLMM.create(this.connection, new PublicKey(this.LOW_FEE_POOL.address));
            
            // Получаем позиции пользователя
            const userPositions = await dlmmPool.getPositionsByUser(this.wallet.publicKey);
            
            if (!userPositions || userPositions.length === 0) {
                console.log('⚠️ Нет позиций пользователя в пуле');
                return { hasFees: false, amount: 0 };
            }
            
            console.log(`📊 Найдено позиций: ${userPositions.length}`);
            
            let totalFees = 0;
            for (const position of userPositions) {
                const feeX = position.positionData.feeX || 0;
                const feeY = position.positionData.feeY || 0;
                totalFees += feeX + feeY;
                
                console.log(`   Позиция ${position.publicKey.toString().slice(0,8)}...: ${feeX + feeY} комиссий`);
            }
            
            const hasSignificantFees = totalFees > 1000000; // > 0.001 SOL в lamports
            
            console.log(`💰 Общие комиссии: ${totalFees} lamports (${totalFees / 1e9} SOL)`);
            console.log(`🎯 Стоит собирать: ${hasSignificantFees ? 'ДА' : 'НЕТ'}`);
            
            return {
                hasFees: hasSignificantFees,
                amount: totalFees,
                positions: userPositions.length
            };
            
        } catch (error) {
            console.error('❌ Ошибка проверки комиссий:', error.message);
            return { hasFees: false, amount: 0, error: error.message };
        }
    }
    
    /**
     * 💰 СБОР КОМИССИЙ С НИЗКОКОМИССИОННОГО ПУЛА
     */
    async collectFees() {
        try {
            console.log('💰 ЗАПУСК СБОРА КОМИССИЙ...');
            console.log(`🎯 Пул: ${this.LOW_FEE_POOL.name}`);
            
            // Проверяем есть ли комиссии для сбора
            const feeCheck = await this.checkAccumulatedFees();
            if (!feeCheck.hasFees) {
                console.log('⚠️ Недостаточно комиссий для сбора');
                return { success: false, reason: 'Недостаточно комиссий' };
            }
            
            // Создаем DLMM instance
            const dlmmPool = await DLMM.create(this.connection, new PublicKey(this.LOW_FEE_POOL.address));
            
            // Получаем позиции пользователя
            const userPositions = await dlmmPool.getPositionsByUser(this.wallet.publicKey);
            
            if (!userPositions || userPositions.length === 0) {
                throw new Error('Нет позиций для сбора комиссий');
            }
            
            console.log(`📊 Собираем комиссии с ${userPositions.length} позиций`);
            
            // Создаем транзакцию сбора комиссий
            const claimFeeTxs = await dlmmPool.claimAllSwapFee({
                owner: this.wallet.publicKey,
                positions: userPositions
            });
            
            if (!claimFeeTxs || claimFeeTxs.length === 0) {
                throw new Error('Не удалось создать транзакцию сбора комиссий');
            }
            
            console.log(`🔧 Создано транзакций: ${claimFeeTxs.length}`);
            
            // Отправляем первую транзакцию
            const transaction = claimFeeTxs[0];
            const result = await this.sendTransaction(transaction);
            
            if (result.success) {
                console.log(`✅ КОМИССИИ СОБРАНЫ УСПЕШНО!`);
                console.log(`📊 Signature: ${result.signature}`);
                console.log(`💰 Собрано с ${userPositions.length} позиций`);
                
                return {
                    success: true,
                    signature: result.signature,
                    positions: userPositions.length,
                    amount: feeCheck.amount
                };
            } else {
                throw new Error(`Ошибка отправки транзакции: ${result.error}`);
            }
            
        } catch (error) {
            console.error('❌ Ошибка сбора комиссий:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * 📤 ОТПРАВКА ТРАНЗАКЦИИ
     */
    async sendTransaction(transaction) {
        try {
            console.log('📤 ОТПРАВКА ТРАНЗАКЦИИ СБОРА КОМИССИЙ...');
            
            // Получаем свежий blockhash
            const { blockhash } = await this.connection.getLatestBlockhash('finalized');
            
            // Создаем versioned transaction
            const messageV0 = new TransactionMessage({
                payerKey: this.wallet.publicKey,
                recentBlockhash: blockhash,
                instructions: transaction.instructions
            }).compileToV0Message();
            
            const versionedTx = new VersionedTransaction(messageV0);
            versionedTx.sign([this.wallet]);
            
            // Отправляем транзакцию
            const signature = await this.connection.sendTransaction(versionedTx, {
                skipPreflight: false,
                preflightCommitment: 'processed'
            });
            
            console.log(`📤 Транзакция отправлена: ${signature}`);
            
            // Ждем подтверждения
            const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');
            
            if (confirmation.value.err) {
                throw new Error(`Транзакция провалилась: ${JSON.stringify(confirmation.value.err)}`);
            }
            
            console.log('✅ Транзакция подтверждена');
            
            return {
                success: true,
                signature: signature
            };
            
        } catch (error) {
            console.error('❌ Ошибка отправки транзакции:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * 🔄 АВТОМАТИЧЕСКИЙ СБОР (ПО РАСПИСАНИЮ)
     */
    async startAutoCollection(intervalMinutes = 60) {
        console.log(`🔄 ЗАПУСК АВТОМАТИЧЕСКОГО СБОРА КОМИССИЙ (каждые ${intervalMinutes} минут)`);
        
        const collectFees = async () => {
            try {
                const result = await this.collectFees();
                if (result.success) {
                    console.log(`✅ Автосбор успешен: ${result.signature}`);
                } else {
                    console.log(`⚠️ Автосбор пропущен: ${result.reason || result.error}`);
                }
            } catch (error) {
                console.error('❌ Ошибка автосбора:', error.message);
            }
        };
        
        // Первый сбор сразу
        await collectFees();
        
        // Затем по расписанию
        setInterval(collectFees, intervalMinutes * 60 * 1000);
    }
}

// 🚀 ЭКСПОРТ И ЗАПУСК
module.exports = MeteoraFeeCollector;

// Если запускается напрямую
if (require.main === module) {
    const collector = new MeteoraFeeCollector();
    
    // Проверяем аргументы командной строки
    const args = process.argv.slice(2);
    
    if (args.includes('--check')) {
        // Только проверка комиссий
        collector.checkAccumulatedFees().then(result => {
            console.log('🔍 РЕЗУЛЬТАТ ПРОВЕРКИ:', result);
            process.exit(0);
        });
    } else if (args.includes('--collect')) {
        // Разовый сбор
        collector.collectFees().then(result => {
            console.log('💰 РЕЗУЛЬТАТ СБОРА:', result);
            process.exit(result.success ? 0 : 1);
        });
    } else if (args.includes('--auto')) {
        // Автоматический сбор
        const interval = parseInt(args[args.indexOf('--auto') + 1]) || 60;
        collector.startAutoCollection(interval);
    } else {
        console.log('🎯 METEORA FEE COLLECTOR');
        console.log('Использование:');
        console.log('  node meteora-fee-collector.js --check     # Проверить комиссии');
        console.log('  node meteora-fee-collector.js --collect   # Собрать комиссии');
        console.log('  node meteora-fee-collector.js --auto 30   # Автосбор каждые 30 минут');
    }
}
